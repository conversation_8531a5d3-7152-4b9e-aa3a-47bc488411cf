# 需求 [migration]

## 反馈

1. 旧的图片需要按照新的目录结构迁移，存储

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 之前的图片没有按照新的目录结构存储

## 解决办法

### 主要技术方案
具体见 docs/20250723_migration.md 文档

### 关键代码优化改进

#### 1. 路径前缀替换安全性改进
**问题**: 原始代码使用不安全的字符串切片操作，可能导致越界访问
```go
// 不安全的方式
if len(originalSrcPath) >= len(OLD_CA6_PATH) && originalSrcPath[:len(OLD_CA6_PATH)] == OLD_CA6_PATH {
    ca7SrcPath = OLD_CA7_PATH + originalSrcPath[len(OLD_CA6_PATH):]
}
```

**解决方案**: 使用 `strings.HasPrefix` 和 `strings.Replace` 进行安全的路径替换
```go
// 安全的方式
if strings.HasPrefix(originalSrcPath, OLD_CA6_PATH) {
    ca7SrcPath = strings.Replace(originalSrcPath, OLD_CA6_PATH, OLD_CA7_PATH, 1)
}
```

#### 2. 哈希冲突处理机制优化
**问题**: 当哈希冲突尝试超过1000次时，返回已存在的原始哈希值，可能导致文件名重复

**解决方案**: 在极端情况下抛出panic，避免数据损坏
```go
if counter > 1000 {
    golog.Error("Too many hash collisions",
        "originalKey", originalKey,
        "originalHash", originalHash,
        "counter", counter)
    panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
}
```

#### 3. MongoDB数据类型安全处理
**问题**: `prop["pho"].(int)` 直接断言易panic，MongoDB读取多为int32/int64

**解决方案**: 创建类型安全的辅助函数
```go
func getIntValue(prop bson.M, key string) int {
    value := prop[key]
    if value == nil {
        return 0
    }

    switch v := value.(type) {
    case int:
        return v
    case int32:
        return int(v)
    case int64:
        return int(v)
    case float64:
        return int(v)
    default:
        golog.Warn("Unexpected type for integer field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
        return 0
    }
}
```

#### 4. 参数验证逻辑优化
**问题**: `buildNewPathWithHash` 函数的参数检查逻辑冗余

**解决方案**: 简化参数验证逻辑
```go
// 优化前
var hash int32
if customHash != 0 {
    hash = customHash
} else {
    return "", fmt.Errorf("customHash is required")
}

// 优化后
if customHash == 0 {
    return "", fmt.Errorf("customHash must be non-zero")
}

hash := customHash
```

## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-04

## online-step

1. 先在ca6上运行，再在ca7上运行
  ```
  cd cmd/photoMigration
  go build 
  ./start.sh  -n migrate_pic -d "goresodownload" -cmd "cmd/photoMigration/bin/photoMigration.bin -disk ca6 -mt "2025-07-23T15:30:00Z" -dryrun
  ```



