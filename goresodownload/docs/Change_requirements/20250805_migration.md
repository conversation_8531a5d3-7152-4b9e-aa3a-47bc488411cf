# 需求 [migration]

## 反馈

1. 旧的图片需要按照新的目录结构迁移，存储

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-07-28

## 原因

1. 之前的图片没有按照新的目录结构存储

## 解决办法

### 主要流程

#### 1. 数据查询和过滤
- 从 MongoDB `listing.properties` 集合中流式读取数据
- 支持按 `mt` 时间戳过滤（可选参数 `-mt "2025-07-23T15:30:00Z"`）
- 按时间倒序处理，确保最新数据优先处理
- 跳过已处理的记录（ca6阶段检查 `phoLH` 字段，ca7阶段检查 `*_old` 字段）

#### 2. 图片路径构建
- **原始路径构建**: 根据不同板块（TRB/DDF/OTW/CLG）的规则构建原始图片路径
- **目标路径生成**: 使用 `golevelstore` 生成新的 L1/L2 目录结构
- **哈希计算**: 对原始路径使用 MurmurHash 算法生成 int32 哈希值
- **哈希冲突处理**: 确保同一个 prop 的所有图片哈希值唯一，冲突时添加后缀重新计算

#### 3. 文件操作执行
- **ca6 磁盘处理**:
  - 优先尝试本地文件重命名（rename）
  - 失败时从 ca7 磁盘复制（copy）作为回退方案
- **ca7 磁盘处理**:
  - 直接进行文件重命名操作
  - 失败时记录错误，不进行回退

#### 4. 数据库更新
- **ca6 阶段**: 更新 prop 表添加 `phoLH`、`tnLH`、`phoP` 字段
- **ca7 阶段**: 将原始字段重命名为 `*_old`（如 `pho` → `pho_old`）
- **rni 表同步**: 根据板块类型更新对应的 rni 记录表
- **缩略图生成**: 为第一张图片生成缩略图并计算 `tnLH`

#### 5. 日志记录和监控
- 记录所有操作结果到 `photo_immigration_ca6/ca7` 表
- 失败操作详细记录错误原因和状态
- 使用 `gospeedmeter` 进行性能监控和进度跟踪
- 支持 dry-run 模式进行安全测试

### 技术实现细节
具体技术方案和代码实现见 docs/20250723_migration.md 文档

### 关键代码优化改进

#### 1. 路径前缀替换安全性改进
**问题**: 原始代码使用不安全的字符串切片操作，可能导致越界访问
```go
// 不安全的方式
if len(originalSrcPath) >= len(OLD_CA6_PATH) && originalSrcPath[:len(OLD_CA6_PATH)] == OLD_CA6_PATH {
    ca7SrcPath = OLD_CA7_PATH + originalSrcPath[len(OLD_CA6_PATH):]
}
```

**解决方案**: 使用 `strings.HasPrefix` 和 `strings.Replace` 进行安全的路径替换
```go
// 安全的方式
if strings.HasPrefix(originalSrcPath, OLD_CA6_PATH) {
    ca7SrcPath = strings.Replace(originalSrcPath, OLD_CA6_PATH, OLD_CA7_PATH, 1)
}
```

#### 2. 哈希冲突处理机制优化
**问题**: 当哈希冲突尝试超过1000次时，返回已存在的原始哈希值，可能导致文件名重复

**解决方案**: 在极端情况下抛出panic，避免数据损坏
```go
if counter > 1000 {
    golog.Error("Too many hash collisions",
        "originalKey", originalKey,
        "originalHash", originalHash,
        "counter", counter)
    panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
}
```

#### 3. MongoDB数据类型安全处理
**问题**: `prop["pho"].(int)` 直接断言易panic，MongoDB读取多为int32/int64

**解决方案**: 创建类型安全的辅助函数
```go
func getIntValue(prop bson.M, key string) int {
    value := prop[key]
    if value == nil {
        return 0
    }

    switch v := value.(type) {
    case int:
        return v
    case int32:
        return int(v)
    case int64:
        return int(v)
    case float64:
        return int(v)
    default:
        golog.Warn("Unexpected type for integer field", "key", key, "type", fmt.Sprintf("%T", v), "value", v)
        return 0
    }
}
```

#### 4. 参数验证逻辑优化
**问题**: `buildNewPathWithHash` 函数的参数检查逻辑冗余

**解决方案**: 简化参数验证逻辑
```go
// 优化前
var hash int32
if customHash != 0 {
    hash = customHash
} else {
    return "", fmt.Errorf("customHash is required")
}

// 优化后
if customHash == 0 {
    return "", fmt.Errorf("customHash must be non-zero")
}

hash := customHash
```

### 实施步骤和注意事项

#### 1. 代码质量改进
- **类型安全**: 所有MongoDB字段访问都使用类型安全的辅助函数
- **错误处理**: 完善的错误日志记录和异常处理机制
- **代码复用**: 统一的路径替换和类型转换函数，减少重复代码

#### 2. 哈希冲突处理策略
- **检测机制**: 使用 `map[int32]bool` 跟踪已使用的哈希值
- **解决策略**: 键名后缀法 (`key_1`, `key_2`, `key_3`...)
- **安全限制**: 最大重试1000次，超过则抛出panic避免数据损坏
- **性能优化**: 大多数情况下第一次重试就能解决冲突

#### 3. 路径处理安全性
- **前缀验证**: 使用 `strings.HasPrefix()` 验证路径前缀
- **安全替换**: 使用 `strings.Replace()` 进行路径替换
- **统一函数**: 创建 `replacePathPrefix()` 辅助函数统一处理

#### 4. 数据验证完整性
- **字段存在性**: 检查必要字段是否存在
- **数据有效性**: 验证数据类型和取值范围
- **数组非空**: 正确检查数组长度而不仅仅是nil判断

### 测试和验证

#### 1. 单元测试覆盖
- 类型转换函数测试
- 哈希冲突处理测试
- 路径替换安全性测试
- 边界条件测试

#### 2. 集成测试
- 完整迁移流程测试
- 错误恢复机制测试
- 性能压力测试
- 数据一致性验证

#### 3. 生产环境验证
- 小批量数据试运行
- 监控日志和错误率
- 性能指标监控
- 数据完整性检查

## 是否需要补充UT

1. 不需要

## 确认日期:    2025-08-04

## online-step

1. 先在ca6上运行，再在ca7上运行
  ```
  cd cmd/photoMigration
  go build 
  ./start.sh  -n migrate_pic -d "goresodownload" -cmd "cmd/photoMigration/bin/photoMigration.bin -disk ca6 -mt "2025-07-23T15:30:00Z" -dryrun
  ```



