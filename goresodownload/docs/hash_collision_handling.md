# 哈希冲突处理机制详细说明

## 概述

在照片迁移过程中，我们使用 MurmurHash 算法为每个图片文件生成唯一的哈希值。由于哈希算法的特性，可能会出现不同的输入产生相同哈希值的情况（哈希冲突）。本文档详细说明了我们的冲突检测和解决机制。

## 哈希冲突检测

### 检测机制
- 使用 `map[int32]bool` 类型的 `usedHashes` 来跟踪已使用的哈希值
- 在为每个图片路径生成哈希时，检查该哈希是否已存在于 `usedHashes` 中
- 如果存在，则判定为哈希冲突

### 检测代码示例
```go
func ensureUniqueHash(originalHash int32, originalKey string, usedHashes map[int32]bool) int32 {
    // 如果哈希值唯一，直接返回
    if !usedHashes[originalHash] {
        return originalHash
    }
    // 检测到冲突，进入解决流程
    // ...
}
```

## 哈希冲突解决

### 解决策略
当检测到哈希冲突时，采用以下策略：

1. **键名后缀法**: 在原始键名后添加递增数字后缀
2. **重新哈希**: 对新的键名重新计算哈希值
3. **唯一性验证**: 检查新哈希值是否唯一
4. **循环重试**: 如果新哈希仍有冲突，继续递增后缀

### 具体实现

#### 后缀格式
- 原始键名: `path/to/image.jpg`
- 第1次冲突: `path/to/image.jpg_1`
- 第2次冲突: `path/to/image.jpg_2`
- 第N次冲突: `path/to/image.jpg_N`

#### 冲突解决循环
```go
counter := 1
for {
    // 创建带后缀的新键名
    newKey := fmt.Sprintf("%s_%d", originalKey, counter)
    newHash := levelStore.MurmurToInt32(newKey)
    
    // 检查新哈希是否唯一
    if !usedHashes[newHash] {
        // 记录冲突解决信息
        golog.Info("Hash collision resolved",
            "originalKey", originalKey,
            "originalHash", originalHash,
            "newKey", newKey,
            "newHash", newHash,
            "counter", counter)
        return newHash
    }
    
    counter++
    
    // 安全检查：防止无限循环
    if counter > 1000 {
        golog.Error("Too many hash collisions",
            "originalKey", originalKey,
            "originalHash", originalHash,
            "counter", counter)
        panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
    }
}
```

## 最大重试次数限制

### 限制设置
- **最大重试次数**: 1000次
- **触发条件**: 当 `counter > 1000` 时触发

### 极端情况处理
当哈希冲突尝试超过1000次时：

#### 旧的处理方式（有问题）
```go
// 问题：返回已存在的原始哈希值，可能导致文件名重复
return originalHash
```

#### 新的处理方式（安全）
```go
// 解决方案：抛出panic，避免数据损坏
panic(fmt.Sprintf("Failed to resolve hash collision after %d attempts for key: %s", counter, originalKey))
```

### 为什么选择 panic 而不是返回错误

1. **数据完整性**: 避免使用重复哈希值导致文件覆盖
2. **问题可见性**: panic 会立即暴露问题，便于调试
3. **系统稳定性**: 防止静默的数据损坏
4. **概率极低**: 1000次冲突的概率极其微小，通常表示系统异常

## 性能考虑

### 哈希冲突概率
- MurmurHash 是高质量的哈希算法，冲突概率很低
- 对于 int32 范围（约42亿个值），在合理的数据量下冲突很少见
- 大多数情况下，第一次重试就能解决冲突

### 内存使用
- `usedHashes` map 的内存使用与处理的图片数量成正比
- 每个 int32 哈希值占用 4 字节
- 对于10万张图片，大约需要 400KB 内存

## 日志记录

### 冲突解决成功
```
INFO Hash collision resolved originalKey=path/to/image.jpg originalHash=12345 newKey=path/to/image.jpg_1 newHash=67890 counter=1
```

### 极端情况（panic）
```
ERROR Too many hash collisions originalKey=path/to/image.jpg originalHash=12345 counter=1001
panic: Failed to resolve hash collision after 1001 attempts for key: path/to/image.jpg
```

## 监控建议

1. **冲突频率监控**: 监控哈希冲突的发生频率
2. **解决时间监控**: 监控冲突解决所需的重试次数
3. **异常告警**: 当冲突重试次数超过阈值（如100次）时发出告警
4. **性能影响**: 监控冲突解决对整体迁移性能的影响

## 测试建议

1. **单元测试**: 测试正常情况和冲突情况
2. **压力测试**: 使用大量数据测试冲突处理性能
3. **边界测试**: 测试接近1000次重试的极端情况
4. **并发测试**: 测试多线程环境下的冲突处理
