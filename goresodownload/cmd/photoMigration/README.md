# Photo Migration Tool

A Go-based command-line tool for migrating photos from old directory structure to new levelstore-based structure for ca6 and ca7 file systems.

## Overview

This tool migrates photos stored in the old directory structure to a new levelstore-based structure, updating database records and generating thumbnails as needed. It follows Go project conventions by being located in the `cmd/` directory.

### Key Features

- **Multi-source support**: Handles TRB, DDF, OTW, and CLG source types
- **Batch processing optimization**: Processes multiple files per property in batches for improved performance
- **Intelligent file operations**: Attempts rename first, falls back to copy when needed
- **Database integration**: Updates MongoDB collections with new path information
- **Thumbnail generation**: Creates thumbnails for the first image of each property
- **Comprehensive logging**: Detailed logs and error tracking
- **Dry-run mode**: Test migrations without actual file operations
- **Time filtering**: Process only records modified after a specific time

## Architecture

The tool is organized into several modules:

- `main.go` - Command-line interface and initialization
- `types.go` - Type definitions and constants
- `path_builder.go` - Path construction logic for different source types
- `db_operations.go` - Database query and update operations
- `file_operations.go` - File system operations (rename, copy, thumbnail)
- `migration.go` - Core migration logic and orchestration

## Prerequisites

- Go 1.24or later
- Access to MongoDB with `listing` and `rni` databases
- Proper configuration files for gobase, golog, and gomongo packages
- Read/write access to ca6 and ca7 mount points

## Installation

```bash
# Navigate to the project directory
cd goresodownload/cmd/photoMigration

# Download dependencies
go mod download

# Build the binary (either method works)
go build -o photoMigration .
# OR
make build
```

## Configuration

The tool uses the existing configuration system from the gobase package. Ensure your configuration includes:

- MongoDB connection settings for `listing` and `rni` databases
- Logging configuration
- Any other required gobase settings

## Usage

### Basic Commands

```bash
# Show help (displays usage information)
bin/photoMigration

# Migrate all photos on ca6 disk
bin/photoMigration -disk ca6

# Migrate photos on ca7 disk
bin/photoMigration -disk ca7

# Dry run (no actual file operations)
bin/photoMigration -disk ca6 -dryrun

# Migrate photos modified after specific time
bin/photoMigration -disk ca6 -mt "2025-07-23T15:30:00Z"
```


### Command-Line Parameters

- `-disk` (required): Specify which disk to process (`ca6` or `ca7`)
- `-mt` (optional): Time filter in RFC3339 format (e.g., `2025-07-23T15:30:00Z`)
- `-dryrun` (optional): Enable dry-run mode for testing
- `-help` (optional): Show detailed help information

## Migration Process

1. **Query Properties**: Retrieves properties from MongoDB with optional time filtering
2. **Filter Processing**: Skips properties that already have `phoLH` field (already migrated)
3. **Path Construction**: Pre-calculates all paths and resolves hash conflicts for each property
4. **Batch File Operations**:
   - Batch checks file existence to reduce I/O operations
   - Batch creates destination directories (deduplicated)
   - Batch executes rename operations for better performance
   - Falls back to copying from alternate disk if rename fails (ca6 only)
5. **Thumbnail Generation**: Creates thumbnails for the first image
6. **Database Updates**: Updates `phoHL` and `tnHL` fields in relevant collections
7. **Logging**: Records all operations and errors

### Performance Optimization

The tool uses batch processing to optimize file operations:
- **1.3-2.1x faster** than individual file processing
- Reduces system calls and disk I/O overhead
- Better cache utilization for files in the same directory
- Particularly effective for properties with multiple images

## Source Type Support

### TRB (Toronto Real Estate Board)
- Path pattern: `treb/mls/{imageNum}/{last3}/{sid}[_{imageNum}].jpg`
- Uses `pho` field to determine image count

### DDF (Data Distribution Facility)  
- Path pattern: `crea/ddf/img/{last3}/{ddfID}_{imageNum}.jpg`
- Uses `pho` field to determine image count

### OTW (Ottawa Real Estate Board)
- Path pattern: `oreb/mls/{mui_last34}/{mui_last12}/{propImgId}_{imageID}.jpg`
- Uses `phoIDs` array to determine images

### CLG (Calgary Real Estate Board)
- Path pattern: `oreb/mls/{mui_last34}/{mui_last12}/{propImgId}_{imageID}.jpg`
- Uses `phoIDs` array to determine images

## Directory Structure

### Old Structure
```
/mnt/ca6m0/mlsimgs/
├── treb/mls/1/567/1234567.jpg
├── crea/ddf/img/174/23214174_1.jpg
└── oreb/mls/80/61/731271_0.jpg
```

### New Structure
```
/mnt/ca6m0/imgs/MLS/
├── 1225/abc12/W4054894_A1B2C3.jpg
├── 1225/def34/23214174_D4E5F6.jpg
└── 1226/ghi56/731271_G7H8I9.jpg
```

## Database Updates

### Properties Collection (`listing.properties`)
- Adds `phoHL` field: Array of int32 hash values for each image
- Adds `tnHL` field: int32 hash value for thumbnail
- Adds `phoP` field: L1/L2 Path to image directory

### RNI Collections (`rni.*_master_records`)
- Adds `phoHL` field: Array of int32 hash values for each image
- Adds `tnHL` field: int32 hash value for thumbnail
- Adds `phoP` field: L1/L2 Path to image directory
- Collection determined by source type:
  - TRB → `mls_treb_master_records`
  - DDF → `mls_crea_ddf_records`
  - OTW → `mls_oreb_master_records`
  - CLG → `mls_creb_master_records`

### Migration Logs (`listing.photo_immigration_ca6/ca7`)
Records migration results including:
- Property ID and timestamps
- Source and destination paths
- Operation status and error messages

## Error Handling

The tool handles various error scenarios:

- **File not found**: Logs error and continues with next file
- **Rename failure**: Attempts copy from alternate disk (ca6 only)
- **Database errors**: Logs error and continues processing
- **Thumbnail generation failure**: Uses original image hash as fallback

## Testing

### Dry Run Mode
Use `-dryrun` flag to test the migration without actual file operations:

```bash
bin/photoMigration -disk ca6 -dryrun
```

This will:
- Query and process all records normally
- Skip actual file operations (rename/copy)
- Skip database updates
- Log what would be done

### Unit Tests
```bash
go test -v ./...
```

## Monitoring and Logs

The tool provides comprehensive logging through the golog package:

- **Info logs**: Normal operation progress
- **Error logs**: Failures and issues
- **Database logs**: Migration results stored in MongoDB

Monitor logs to track:
- Processing progress
- Success/failure rates
- Performance metrics
- Error patterns

## Production Deployment

### Recommended Sequence
1. Run ca6 migration first: `bin/photoMigration -disk ca6`
2. Run ca7 migration second: `bin/photoMigration -disk ca7`

### Performance Considerations
- Uses batch processing for optimal file operation performance
- Process runs single-threaded to avoid file system conflicts
- Large datasets may take considerable time, but batch optimization reduces overall time
- Monitor disk space during migration
- Consider running during low-traffic periods

### Batch Processing Benefits
- **Reduced I/O operations**: Batch file existence checks and directory creation
- **Better cache utilization**: Sequential operations on files in same directory
- **Lower system call overhead**: Fewer context switches between user/kernel space
- **Scalable performance**: Improvement increases with number of images per property

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure read/write access to mount points
2. **Database connection**: Verify MongoDB configuration
3. **Memory usage**: Monitor for large result sets
4. **Disk space**: Ensure sufficient space for new directory structure

### Recovery

If migration is interrupted:
- Records with `phoLH` field are considered complete
- Re-run the tool to continue from where it left off
- Use time filtering (`-mt`) to process recent records only

## Development

### Building
```bash
go build -o photoMigration .                    # Standard build
go build -race -o photoMigration .              # Development build with race detection
CGO_ENABLED=0 go build -o photoMigration .      # Production build (optimized)
```

### Code Quality
```bash
gofmt -l .                    # Check formatting
gofmt -w .                    # Fix formatting
golangci-lint run             # Run linter (requires golangci-lint)
```

## Support

For issues or questions:
1. Check the logs for detailed error information
2. Verify configuration and permissions
3. Test with dry-run mode first
4. Review the technical specification document
