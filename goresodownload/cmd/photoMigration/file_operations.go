package main

import (
	"fmt"
	_ "image/jpeg" // Support JPEG decoding
	_ "image/png"  // Support PNG decoding
	"os"
	"path/filepath"
	"strings"

	gofile "github.com/real-rm/gofile"
	gohelper "github.com/real-rm/gohelper"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	"go.mongodb.org/mongo-driver/bson"
)

// fileExists checks if a file exists at the given path
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// ensureDir ensures that the directory for the given file path exists
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create directory %s: %w", dir, err)
	}
	return nil
}

// renameFile renames a file from source to destination
func renameFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	if err := os.Rename(src, dst); err != nil {
		return fmt.Errorf("failed to rename %s to %s: %w", src, dst, err)
	}

	golog.Info("File renamed successfully", "src", src, "dst", dst)
	return nil
}

// copyFile copies a file from source to destination
func copyFile(src, dst string) error {
	// Ensure destination directory exists
	if err := ensureDir(dst); err != nil {
		return err
	}

	// Open source file
	srcFile, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("failed to open source file %s: %w", src, err)
	}
	defer srcFile.Close()

	// Create destination file
	dstFile, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("failed to create destination file %s: %w", dst, err)
	}
	defer dstFile.Close()

	// Copy content
	if _, err := srcFile.WriteTo(dstFile); err != nil {
		return fmt.Errorf("failed to copy content from %s to %s: %w", src, dst, err)
	}

	golog.Info("File copied successfully", "src", src, "dst", dst)
	return nil
}

// renameOrCopy attempts to rename file, falls back to copy if rename fails
// This implements the ca6/ca7 fallback strategy as specified in the migration document
func renameOrCopy(srcPath, dstPath, disk string) (string, error) {
	golog.Info("Starting file operation", "src", srcPath, "dst", dstPath, "disk", disk)

	// Check if source file exists on local path
	if !fileExists(srcPath) {
		golog.Error("Source file not found on local path", "path", srcPath, "disk", disk)

		// If ca6 disk and source file not found, try to copy from ca7 as fallback
		if disk == "ca6" {
			return copyFromCA7Fallback(srcPath, dstPath)
		}

		// For ca7 disk, if file not found, return error directly
		return STATUS_NOT_FOUND, fmt.Errorf("file not found on %s: %s", disk, srcPath)
	}

	// First attempt to rename (move within same disk)
	golog.Info("Attempting to rename file", "src", srcPath, "dst", dstPath)
	if err := renameFile(srcPath, dstPath); err == nil {
		golog.Info("File renamed successfully", "src", srcPath, "dst", dstPath)
		return STATUS_RENAMED, nil
	} else {
		golog.Error("Rename failed", "src", srcPath, "dst", dstPath, "error", err)

		// If ca6 disk and rename failed, try to copy from ca7 as fallback
		if disk == "ca6" {
			golog.Info("Rename failed on ca6, trying ca7 fallback", "src", srcPath)
			return copyFromCA7Fallback(srcPath, dstPath)
		}

		// For ca7 disk, if rename fails, return error directly
		return STATUS_RENAME_FAILED, fmt.Errorf("rename failed on %s: %w", disk, err)
	}
}

// copyFromCA7Fallback attempts to copy file from ca7 when ca6 operation fails
func copyFromCA7Fallback(originalSrcPath, dstPath string) (string, error) {
	// Construct ca7 path by replacing local prefix with ca7
	ca7SrcPath := originalSrcPath
	if strings.HasPrefix(originalSrcPath, OLD_LOCAL_PATH) {
		ca7SrcPath = strings.Replace(originalSrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH, 1)
	}

	golog.Info("Attempting ca7 fallback copy", "ca7_src", ca7SrcPath, "dst", dstPath)

	// Check if file exists on ca7
	if !fileExists(ca7SrcPath) {
		golog.Error("File not found on ca7 fallback", "path", ca7SrcPath)
		return STATUS_NOT_FOUND, fmt.Errorf("file not found on ca7 fallback: %s", ca7SrcPath)
	}

	// Attempt to copy from ca7
	if err := copyFile(ca7SrcPath, dstPath); err != nil {
		golog.Error("Ca7 fallback copy failed", "ca7_src", ca7SrcPath, "dst", dstPath, "error", err)
		return STATUS_COPY_FAILED, fmt.Errorf("ca7 fallback copy failed: %w", err)
	}

	golog.Info("Ca7 fallback copy successful", "ca7_src", ca7SrcPath, "dst", dstPath)
	return STATUS_COPIED, nil
}

// createThumbnail creates a thumbnail from the original image using predetermined hash
func createThumbnail(originalPath, thumbnailPath string, thumbnailHash int32) error {
	// Check if original file exists
	if !fileExists(originalPath) {
		return fmt.Errorf("original file not found: %s", originalPath)
	}

	// Read the original image file
	imageData, err := os.ReadFile(originalPath)
	if err != nil {
		return fmt.Errorf("failed to read image file: %w", err)
	}

	// Resize image using gofile's ResizeImageFromData
	// Using same dimensions as goresodownload.THUMBNAIL_WIDTH/HEIGHT (240x160)
	resizedImg, err := gofile.ResizeImageFromData(imageData, 240, 160)
	if err != nil {
		return fmt.Errorf("failed to resize image: %w", err)
	}

	// Ensure thumbnail directory exists
	if err := ensureDir(thumbnailPath); err != nil {
		return err
	}

	// Save thumbnail as JPEG
	savedPath, err := gofile.SaveImage(resizedImg, thumbnailPath, false) // false = JPEG format
	if err != nil {
		return fmt.Errorf("failed to save thumbnail: %w", err)
	}

	golog.Info("Thumbnail created", "original", originalPath, "thumbnail", savedPath, "hash", thumbnailHash)
	return nil
}

// processImageFileWithHash processes a single image file with predetermined hash
func processImageFileWithHash(prop bson.M, relativePath string, hash int32, config Config) (*ImageInfo, error) {
	// Build full source path
	// Always use local path as default first, fallback to ca7 if not found
	basePath := OLD_LOCAL_PATH
	srcPath := filepath.Join(basePath, relativePath)
	golog.Debug("Built original path with custom hash", "srcPath", srcPath, "hash", hash)

	// Extract sid
	sid, ok := prop["sid"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid sid field")
	}

	// Generate base path - handle different integer types from MongoDB
	var onD int
	switch v := prop["onD"].(type) {
	case int:
		onD = v
	case int32:
		onD = int(v)
	case int64:
		onD = int(v)
	default:
		return nil, fmt.Errorf("invalid onD field type: %T", prop["onD"])
	}
	ts := gohelper.DateToTime(onD)

	src, ok := prop["src"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid src field")
	}

	basePath, err := levelStore.GetFullFilePathForProp(ts, src, sid)
	if err != nil {
		return nil, fmt.Errorf("failed to get file path: %w", err)
	}

	// Build new path using predetermined hash
	newPath, err := buildNewPathWithHash(basePath, sid, hash)
	if err != nil {
		return nil, fmt.Errorf("failed to build new path with hash: %w", err)
	}

	// Build full destination path with SRC subdirectory
	var newBasePath string
	if config.Disk == "ca6" {
		newBasePath = NEW_CA6_PATH
	} else {
		newBasePath = NEW_CA7_PATH
	}
	// Add SRC subdirectory: /mnt/ca6m0/imgs/MLS/TRB/995/1cb3c/...
	newBasePathWithSrc := filepath.Join(newBasePath, src)
	dstPath := filepath.Join(newBasePathWithSrc, newPath)

	// Generate base62 for the predetermined hash
	base62, err := levelStore.Int32ToBase62(hash)
	if err != nil {
		return nil, fmt.Errorf("failed to generate base62: %w", err)
	}

	imageInfo := &ImageInfo{
		OriginalPath: srcPath,
		NewPath:      dstPath,
		Hash:         hash,
		Base62:       base62,
	}

	// Perform actual file operations if not in dry run mode
	if !config.DryRun {
		status, err := renameOrCopy(srcPath, dstPath, config.Disk)
		if err != nil {
			return imageInfo, fmt.Errorf("file operation failed: %w", err)
		}
		golog.Info("File processed with custom hash", "status", status, "src", srcPath, "dst", dstPath, "hash", hash)
	} else {
		golog.Info("DRY RUN: Would process file with custom hash", "src", srcPath, "dst", dstPath, "hash", hash)
	}

	return imageInfo, nil
}

// FileOperation represents a single file operation to be performed
type FileOperation struct {
	RelativePath string
	NewPath      string
	Hash         int32
	SrcPath      string
	DstPath      string
	Base62       string
}

// BatchOperationResult represents the result of a batch file operation
type BatchOperationResult struct {
	ImageInfos []*ImageInfo
	Success    int
	Failed     int
	Errors     []error
}

// batchRenameOrCopy performs batch file operations for better performance
func batchRenameOrCopy(operations []FileOperation, config Config) (*BatchOperationResult, error) {
	result := &BatchOperationResult{
		ImageInfos: make([]*ImageInfo, 0, len(operations)),
		Success:    0,
		Failed:     0,
		Errors:     []error{},
	}

	if config.DryRun {
		// In dry run mode, just create ImageInfo objects without actual file operations
		for _, op := range operations {
			imageInfo := &ImageInfo{
				OriginalPath: op.SrcPath,
				NewPath:      op.DstPath,
				Hash:         op.Hash,
				Base62:       op.Base62,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("DRY RUN: Would process file", "src", op.SrcPath, "dst", op.DstPath, "hash", op.Hash)
		}
		return result, nil
	}

	// Phase 1: Batch check file existence and collect valid operations
	validOps := make([]FileOperation, 0, len(operations))
	ca7FallbackOps := make([]FileOperation, 0)

	for _, op := range operations {
		if fileExists(op.SrcPath) {
			validOps = append(validOps, op)
		} else {
			golog.Error("Source file not found", "path", op.SrcPath, "disk", config.Disk)

			// If ca6 disk and source file not found on local path, prepare for ca7 fallback
			if config.Disk == "ca6" {
				ca7Op := op
				// Construct ca7 path by replacing local prefix with ca7
				if strings.HasPrefix(op.SrcPath, OLD_LOCAL_PATH) {
					ca7Op.SrcPath = strings.Replace(op.SrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH, 1)
					ca7FallbackOps = append(ca7FallbackOps, ca7Op)
				} else {
					result.Failed++
					result.Errors = append(result.Errors, fmt.Errorf("file not found: %s", op.SrcPath))
				}
			} else {
				result.Failed++
				result.Errors = append(result.Errors, fmt.Errorf("file not found on %s: %s", config.Disk, op.SrcPath))
			}
		}
	}

	// Phase 2: Batch create destination directories
	dirSet := make(map[string]bool)
	for _, op := range validOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}
	for _, op := range ca7FallbackOps {
		dir := filepath.Dir(op.DstPath)
		dirSet[dir] = true
	}

	for dir := range dirSet {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return result, fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	// Phase 3: Batch rename operations
	for _, op := range validOps {
		if err := os.Rename(op.SrcPath, op.DstPath); err != nil {
			golog.Error("Rename failed", "src", op.SrcPath, "dst", op.DstPath, "error", err)

			// If ca6 disk and rename failed on local path, add to ca7 fallback
			if config.Disk == "ca6" {
				ca7Op := op
				if strings.HasPrefix(op.SrcPath, OLD_LOCAL_PATH) {
					ca7Op.SrcPath = strings.Replace(op.SrcPath, OLD_LOCAL_PATH, OLD_CA7_PATH, 1)
					ca7FallbackOps = append(ca7FallbackOps, ca7Op)
				} else {
					result.Failed++
					result.Errors = append(result.Errors, fmt.Errorf("rename failed: %w", err))
				}
			} else {
				result.Failed++
				result.Errors = append(result.Errors, fmt.Errorf("rename failed on %s: %w", config.Disk, err))
			}
		} else {
			// Success
			imageInfo := &ImageInfo{
				OriginalPath: op.SrcPath,
				NewPath:      op.DstPath,
				Hash:         op.Hash,
				Base62:       op.Base62,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("File renamed successfully", "src", op.SrcPath, "dst", op.DstPath)
		}
	}

	// Phase 4: Handle ca7 fallback operations (batch copy)
	for _, op := range ca7FallbackOps {
		if !fileExists(op.SrcPath) {
			golog.Error("File not found on ca7 fallback", "path", op.SrcPath)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("file not found on ca7 fallback: %s", op.SrcPath))
			continue
		}

		if err := copyFile(op.SrcPath, op.DstPath); err != nil {
			golog.Error("Ca7 fallback copy failed", "ca7_src", op.SrcPath, "dst", op.DstPath, "error", err)
			result.Failed++
			result.Errors = append(result.Errors, fmt.Errorf("ca7 fallback copy failed: %w", err))
		} else {
			imageInfo := &ImageInfo{
				OriginalPath: op.SrcPath,
				NewPath:      op.DstPath,
				Hash:         op.Hash,
				Base62:       op.Base62,
			}
			result.ImageInfos = append(result.ImageInfos, imageInfo)
			result.Success++
			golog.Info("Ca7 fallback copy successful", "ca7_src", op.SrcPath, "dst", op.DstPath)
		}
	}

	return result, nil
}

// batchProcessImageFiles processes multiple image files with pre-calculated paths and hashes using batch operations
func batchProcessImageFiles(imagePaths []string, imageHashMap map[string]int32, imagePathMap map[string]string, src string, config Config) (*BatchOperationResult, error) {
	// Prepare batch operations
	operations := make([]FileOperation, 0, len(imagePaths))

	// Build base paths once
	var srcBasePath, dstBasePath string
	// Always use local path as default first, fallback to ca7 if not found
	srcBasePath = OLD_LOCAL_PATH
	if config.Disk == "ca6" {
		dstBasePath = NEW_CA6_PATH
	} else {
		dstBasePath = NEW_CA7_PATH
	}

	for _, relativePath := range imagePaths {
		hash := imageHashMap[relativePath]
		newPath := imagePathMap[relativePath]

		// Generate base62 for the hash
		base62, err := levelStore.Int32ToBase62(hash)
		if err != nil {
			return nil, fmt.Errorf("failed to generate base62 for %s: %w", relativePath, err)
		}

		operation := FileOperation{
			RelativePath: relativePath,
			NewPath:      newPath,
			Hash:         hash,
			SrcPath:      filepath.Join(srcBasePath, relativePath),
			DstPath:      filepath.Join(dstBasePath, src, newPath), // Add SRC subdirectory
			Base62:       base62,
		}
		operations = append(operations, operation)
	}

	// Perform batch operations
	return batchRenameOrCopy(operations, config)
}
