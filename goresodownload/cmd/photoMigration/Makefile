# Photo Migration Tool Makefile

# Binary name
BINARY_NAME=photoMigration
BUILD_DIR=bin

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

# Build flags
LDFLAGS:=-ldflags "-X main.Version=$(shell git describe --tags --always --dirty) -X main.BuildTime=$(shell date -u '+%Y-%m-%d_%H:%M:%S')"

.PHONY: all build clean test deps help install run-ca6 run-ca7 dry-run

# Default target
all: clean deps build

# Build the binary
build:
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME).bin .
	@echo "Build completed: $(BUILD_DIR)/$(BINARY_NAME).bin"

# Clean build artifacts
clean:
	@echo "Cleaning..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@echo "Clean completed"

# Run tests
test:
	@echo "Running tests..."
	$(GOTEST) -v ./...

# Download dependencies
deps:
	@echo "Downloading dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

# Install the binary to GOPATH/bin
install: build
	@echo "Installing $(BINARY_NAME)..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) $(GOPATH)/bin/
	@echo "Installed to $(GOPATH)/bin/$(BINARY_NAME)"

# Development shortcuts
run-ca6: build
	@echo "Running migration for ca6..."
	./$(BUILD_DIR)/$(BINARY_NAME) -disk ca6

run-ca7: build
	@echo "Running migration for ca7..."
	./$(BUILD_DIR)/$(BINARY_NAME) -disk ca7

dry-run: build
	@echo "Running dry-run for ca6..."
	./$(BUILD_DIR)/$(BINARY_NAME) -disk ca6 -dryrun

# Run with time filter (example)
run-with-time: build
	@echo "Running migration with time filter..."
	./$(BUILD_DIR)/$(BINARY_NAME) -disk ca6 -mt "2025-07-23T15:30:00Z"

# Show help
help: build
	./$(BUILD_DIR)/$(BINARY_NAME) -help

# Development build (with race detection)
build-dev:
	@echo "Building $(BINARY_NAME) with race detection..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) -race $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-dev .

# Production build (optimized)
build-prod:
	@echo "Building $(BINARY_NAME) for production..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=0 GOOS=linux $(GOBUILD) -a -installsuffix cgo $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-prod .

# Check code formatting
fmt:
	@echo "Checking code formatting..."
	@gofmt -l . | grep -v vendor/ || true

# Fix code formatting
fmt-fix:
	@echo "Fixing code formatting..."
	@gofmt -w .

# Run linter (requires golangci-lint)
lint:
	@echo "Running linter..."
	@golangci-lint run

# Show project info
info:
	@echo "Project: Photo Migration Tool"
	@echo "Binary: $(BINARY_NAME)"
	@echo "Build Dir: $(BUILD_DIR)"
	@echo "Go Version: $(shell go version)"
	@echo "Git Commit: $(shell git rev-parse --short HEAD 2>/dev/null || echo 'unknown')"
	@echo "Build Time: $(shell date -u '+%Y-%m-%d %H:%M:%S UTC')"
